package com.faw.work.ais.aic.model.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;
import java.time.LocalDateTime;

/**
 * 相似问表实体类
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("faq_similar_knowledge")
public class FaqSimilarKnowledgePO {
    
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;
    
    /**
     * 原问题（对应faq_knowledge的question）
     */
    private String question;
    
    /**
     * 相似问问题
     */
    private String similarQuestion;
    
    /**
     * 知识分类id
     */
    private String categoryId;

    
    /**
     * 答案
     */
    private String answer;
    
    /**
     * 关联的原问题ID（faq_knowledge.id）
     */
    private String knowledgeId;
    
    /**
     * 创建人
     */
    private String createdBy;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;


    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
} 