package com.faw.work.ais.aic.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;

/**
 * 新增文档请求
 *
 * <AUTHOR> Assistant
 */
@Data
@Validated
@Schema(description = "新增文档请求")
public class RagDocumentAddRequest {

    @NotNull(message = "类目ID不能为空")
    @Schema(description = "类目ID")
    private Long categoryId;



    @Schema(description = "上传的文件")
    private MultipartFile file;


    @Schema(description = "分段策略(00-智能切分 01-自定义切分)")
    @NotNull(message = "分段策略不能为空")
    private String chunkStrategy;

    @Schema(description = "分段标识符(默认双换行符\\n ，。！等)")
    private String chunkSeparator;

    @Schema(description = "分段最大长度(tokens)")
    @NotNull(message = "分段最大长度不能为空")
    private Integer chunkLength;

    @Schema(description = "分段重叠长度(tokens)")
    @NotNull(message = "分段重叠长度不能为空")
    private Integer overlapLength;

    @Schema(description = "标签")
    @NotNull(message = "标签不能为空")
    private String label;

    @Schema(description = "数据解析方式（01-代码切分 02-大模型切分）")
    @NotNull(message = "数据解析方式不能为空")
    private String parseType;
} 