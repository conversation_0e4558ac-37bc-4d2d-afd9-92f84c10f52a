package com.faw.work.ais.controller;

import com.faw.work.ais.common.Response;
import com.faw.work.ais.common.exception.BizException;
import com.faw.work.ais.entity.dto.ai.NumberEmployeeDTO;
import com.faw.work.ais.entity.dto.ai.ProcessInfoDTO;
import com.faw.work.ais.entity.vo.ai.AiCoverDicVO;
import com.faw.work.ais.entity.vo.ai.BizUnitInfoVO;
import com.faw.work.ais.entity.vo.ai.FlowInfoVO;
import com.faw.work.ais.entity.vo.ai.NumberEmployeeVO;
import com.faw.work.ais.service.NumberEmployeePlusService;
import com.faw.work.ais.service.NumberEmployeeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * NumberEmployeeController 数字员工
 * <AUTHOR>
 * @date 2024/06/31
 */

@Schema(description = "数字员工")
@Slf4j
@RestController("NumberEmployeeController")
@RequestMapping("/number")
public class NumberEmployeeController {

    @Autowired
    NumberEmployeeService numberEmployeeService;

    @Autowired
    NumberEmployeePlusService numberEmployeePlusService;

    @Operation(summary = "数字员工看板", description = "[author:10236535]")
    @PostMapping(value = "/borad")
    public Response<NumberEmployeeVO> borad(@RequestBody @Valid NumberEmployeeDTO numberEmployeeDTO) {
        try {
            NumberEmployeeVO numberEmployeeVO = numberEmployeeService.getInfo(numberEmployeeDTO);
            Response<NumberEmployeeVO> response = new Response();
            response.setMessage("");
            response.setData(numberEmployeeVO);
            return response;
        } catch (Exception e) {
            log.error("数字员工看板异常" + e.getMessage(), e);
            return Response.fail("数字员工看板异常");
        }
    }

    @Operation(summary = "l3下拉列表", description = "[author:10236535]")
    @PostMapping(value = "/l3FlowBusiness")
    public Response<List<FlowInfoVO>> l3FlowBusiness() {
        try {
            List<FlowInfoVO> list = numberEmployeeService.getL3FlowBusiness();
            Response<List<FlowInfoVO>> response = new Response();
            response.setMessage("");
            response.setData(list);
            return response;
        } catch (Exception e) {
            log.error("l3下拉列表" + e.getMessage(), e);
            return Response.fail("l3下拉列表");
        }
    }

    @Operation(summary = "业务单元下拉列表", description = "[author:10236535]")
    @PostMapping(value = "/bizUnitInfos")
    public Response<List<BizUnitInfoVO>> bizUnitInfos(@RequestBody ProcessInfoDTO processInfoDTO) {
        try {
            List<BizUnitInfoVO> list = numberEmployeeService.getl3BizUnitInfos(processInfoDTO);
            Response<List<BizUnitInfoVO>> response = new Response();
            response.setMessage("");
            response.setData(list);
            return response;
        } catch (Exception e) {
            log.error("业务单元下拉列表" + e.getMessage(), e);
            return Response.fail("业务单元下拉列表");
        }
    }


    @Operation(summary = "AI覆盖角色数", description = "[author:10236535]")
    @PostMapping(value = "/aiCoverRoleNum")
    public Response<String> aiCoverRoleNum() {
        try {
            String result = numberEmployeeService.getAiCoverRoleNum();
            Response<String> response = new Response();
            response.setMessage("");
            response.setData(result);
            return response;
        } catch (Exception e) {
            log.error("数字员工看板异常" + e.getMessage(), e);
            return Response.fail("数字员工看板异常");
        }
    }

    @Operation(summary = "AI覆盖业务单元数", description = "[author:10236535]")
    @PostMapping(value = "/aiCoverBizNum")
    public Response<String> aiCoverBizNum() {
        try {
            String result = numberEmployeeService.getAiCoverBizNum();
            Response<String> response = new Response();
            response.setMessage("");
            response.setData(result);
            return response;
        } catch (Exception e) {
            log.error("数字员工看板异常" + e.getMessage(), e);
            return Response.fail("数字员工看板异常");
        }
    }

    @Operation(summary = "AI覆盖规则数", description = "[author:10236535]")
    @PostMapping(value = "/aiCoverRuleNum")
    public Response<String> aiCoverRuleNum() {
        try {
            String result = numberEmployeeService.getAiCoverRuleNum();
            Response<String> response = new Response();
            response.setMessage("");
            response.setData(result);
            return response;
        } catch (Exception e) {
            log.error("数字员工看板异常" + e.getMessage(), e);
            return Response.fail("数字员工看板异常");
        }
    }

    @Operation(summary = "数字员工看板-新", description = "[author:10236535]")
    @PostMapping(value = "/boradNew")
    public Response<NumberEmployeeVO> boradNew(@RequestBody @Valid NumberEmployeeDTO numberEmployeeDTO) {
        try {
//            NumberEmployeeVO numberEmployeeVO = numberEmployeeService.getInfoNew(numberEmployeeDTO);
            NumberEmployeeVO numberEmployeeVO = numberEmployeePlusService.getInfoThreadNew(numberEmployeeDTO);
            Response<NumberEmployeeVO> response = new Response();
            response.setMessage("");
            response.setData(numberEmployeeVO);
            return response;
        } catch (BizException e) {
            log.error("数字员工看板异常" + e.getMessage(), e);
            return Response.fail(e.getErrorEnum().msg());
        }
    }

    @Operation(summary = "数字员工看板-新-采用线程池", description = "[author:10236535]")
    @PostMapping(value = "/boradNewThread")
    public Response<NumberEmployeeVO> boradNewThread(@RequestBody @Valid NumberEmployeeDTO numberEmployeeDTO) {
        try {
            NumberEmployeeVO numberEmployeeVO = numberEmployeePlusService.getInfoThreadNew(numberEmployeeDTO);
            Response<NumberEmployeeVO> response = new Response();
            response.setMessage("");
            response.setData(numberEmployeeVO);
            return response;
        } catch (BizException e) {
            log.error("数字员工看板异常" + e.getMessage(), e);
            return Response.fail(e.getErrorEnum().msg());
        }
    }
    @Operation(summary = "AI覆盖规则上线率", description = "[author:10236535]")
    @PostMapping(value = "/aiCoverRuleRate")
    public Response<AiCoverDicVO> aiCoverRuleRate() {
        try {
            AiCoverDicVO result = numberEmployeeService.getAiCoverRuleRate();
            Response<AiCoverDicVO> response = new Response();
            response.setMessage("");
            response.setData(result);
            return response;
        } catch (Exception e) {
            log.error("数字员工看板异常" + e.getMessage(), e);
            return Response.fail("数字员工看板异常");
        }
    }
}
