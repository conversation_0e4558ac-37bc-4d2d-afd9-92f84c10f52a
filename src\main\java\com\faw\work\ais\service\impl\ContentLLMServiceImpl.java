package com.faw.work.ais.service.impl;

import com.alibaba.fastjson.JSON;
import com.faw.work.ais.common.CommonConstants;
import com.faw.work.ais.common.dto.chat.*;
import com.faw.work.ais.entity.dto.ContentReviewResult;
import com.faw.work.ais.entity.request.CommentSummaryRequest;
import com.faw.work.ais.entity.request.PostSummaryRequest;
import com.faw.work.ais.feign.content.AliFeignClient;
import com.faw.work.ais.service.ContentLLMService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
@Slf4j
public class ContentLLMServiceImpl implements ContentLLMService {

    public static final String COMMENT_FEATURE_PROMPT = """
        你是一名专业的评论分析专家。请仅根据下列红旗智联app的评论[主题]、[内容]及其[标签]（优质评论/劣质评论），分析这些评论被标记为该标签的主要原因和特征。
        
        输出要求：
        - 需对所有入参评论，分别统计优质评论和劣质评论的特征集合（去重），每个特征下需给出对应的原始评论示例（可多条），每个示例需带上主题，格式为"主题:xxx 评论:xxx"。
        - 输出格式如下：
        {
            "优质特征": {
                "特征1": ["主题:xxx 评论:xxx", "主题:yyy 评论:yyy"],
                "特征2": ["主题:zzz 评论:zzz"]
            },
            "劣质特征": {
                "特征A": ["主题:aaa 评论:aaa"],
                "特征B": ["主题:bbb 评论:bbb", "主题:ccc 评论:ccc"]
            }
        }
        - 仅输出严格合法的JSON，禁止输出多余文本、注释或解释。
        - 仅基于可见内容判断，禁止主观猜测或假设。
        - 确保JSON格式完全正确。
        """;
    public static final String COMMENT_SUMMARY_PROMPT = """
        你是一名专业的评论评分规则制定专家。请根据下列红旗智联app的评论特征分析结果和初始版评分规则，完善一套更科学、细致、可落地的【评论评分规则】。
        
        输入内容包括：
        - 多条评论的特征与主要原因（JSON格式）
        - 初始版评分规则
        
        输出要求：
        - 仅输出严格合法的JSON评分规则，结构与初始版一致。
        - 必须结合评论特征和主要原因，细化和优化评分维度、分值和判定标准。
        - 评分标准的总分必须为100分
        - 评分标准必须详细、具体、可操作，避免模糊描述。
        - 严禁输出任何多余文本、注释或解释。
        - 仅基于输入内容判断，禁止主观猜测或假设。
        - 如果初始版评分规则包含分析结果或相似的分析结果，则可以不对初始版规则进行修改直接原封不动返回
        - 确保JSON格式完全正确。
        """;

    public static final String COMMENT_PRE = """
        1. 如果评论中出现黄色暴力、不文明用语、广告这些内容，认为是较差评论-1分并分析原因直接返回严格合法的json，不再进行后续评分，若没出现则忽略这条标准
        {
            "违规": {"score": -1, "reason": "原因分析"}
        }
        1. 如果评论中只有毫无意义的数字或字符或表情，认为是较差评论0分并分析原因直接返回严格合法的json，不再进行后续评分，若没出现则忽略这条标准
        {
            "无意义": {"score": 0, "reason": "原因分析"}
        }
        1. 如果评论中出现车辆出现的故障，消极态度认为是较差评论10分并分析原因直接返回严格合法的json，不再进行后续评分，若没出现则忽略这条标准
        {
            "故障": {"score": 10, "reason": "原因分析"}
        }
        1. 如果评论是疑问语气，并且询问红旗车辆的相关问题，需要提到具体询问内容，注意不是客服的语气，得110分并分析原因直接返回严格合法的json，不再进行后续评分，若没出现则忽略这条标准
        {
            "咨询": {"score": 110, "reason": "原因分析"}
        }""";
    public static final String COMMENT_PROMPT = """
        你是一个专业的红旗智联评论审核助手，下列提供了动态主题和评论，请严格按如下规则为评论打分：
        %s
        1. 按总分100分制对以下%s项独立评分：
        %s
        2. 输出格式必须为严格合法的JSON：
        %s
        3. 必须遵守：
        - 返回字数不超过100字
        - 仅基于可见内容判断，禁止猜测或假设
        - 评分原因需包含具体数据
        - 确保JSON格式正确（引号、逗号、括号闭合）
        - 不要添加任何额外文本
        """;

    public static final String POST_FEATURE_PROMPT = """
        你是一名专业的动态分析专家。请仅根据下列红旗智联app的动态[主题]、[内容]、[图片]及其[标签]（优质动态/劣质动态），分析这些动态被标记为该标签的主要原因和特征。
        
        输出要求：
        - 需对所有入参动态，分别统计优质动态和劣质动态的特征集合（去重）
        - 输出格式如下：
        {
            "优质特征": {
                "特征1",
                "特征2"
            },
            "劣质特征": {
                "特征A",
                "特征B"
            }
        }
        - 仅输出严格合法的JSON，禁止输出多余文本、注释或解释。
        - 仅基于可见内容判断，禁止主观猜测或假设。
        - 确保JSON格式完全正确。
        """;
    public static final String POST_SUMMARY_PROMPT = """
        你是一名专业的动态评分规则制定专家。请根据下列红旗智联app的动态特征分析结果和初始版评分规则，完善一套更科学、细致、可落地的【动态评分规则】。请确保规则能高度符合人工标注标准，能准确区分优质与劣质动态。
        
        输入内容包括：
        - 多条动态的特征与主要原因（JSON格式）
        - 初始版评分规则
        
        输出要求：
        - 仅输出严格合法的JSON评分规则，结构与初始版一致。
        - 必须结合动态特征和主要原因，细化和优化评分维度、分值和判定标准。
        - 评分标准的总分必须为100分
        - 评分标准必须详细、具体、可操作，避免模糊描述。
        - 严禁输出任何多余文本、注释或解释。
        - 仅基于输入内容判断，禁止主观猜测或假设。
        - 如果初始版评分规则包含相似的分析结果，则可以不对初始版规则进行修改直接原封不动返回
        - 确保JSON格式完全正确。
        """;

    public static final String POST_PRE = """
        1. 如果动态中出现【黄色暴力】、【不文明用语】,认为是违规动态得-1分，并分析原因直接返回严格合法的json，不再进行后续评分，若没出现则忽略这条标准
        注意：此规则只考虑违规性，一定不要考虑和话题的相关性以及是否符合话题要求
        {
            "违规": {"score": -1, "reason": "原因分析"}
        }
        1. 如果动态中表达出对红旗的不满，认为是较差动态0分并分析原因直接返回严格合法的json，不再进行后续评分，若没出现则忽略这条标准
        {
            "不满": {"score": 0, "reason": "原因分析"}
        }
        1. 如果动态内容中只有毫无意义的数字或字符或表情，认为是较差动态0分并分析原因直接返回严格合法的json，不再进行后续评分，若没出现则忽略这条标准
        {
            "无意义": {"score": 0, "reason": "原因分析"}
        }""";

    public static final String POST_PROMPT = """
        你是一个专业的红旗智联app动态审核助手，下列提供了动态话题、正文内容和图片列表，请严格按如下规则为动态打分：
        %s
        1. 按总分100分制对以下%s项独立评分：
        %s
        2. 输出格式必须为严格合法的JSON：
        %s
        3. 必须遵守：
        - 返回字数不超过100字
        - 仅基于可见内容判断，禁止猜测或假设
        - 评分原因需包含具体数据
        - 确保JSON格式正确（引号、逗号、括号闭合）
        - 不要添加任何额外文本
        """;

    public static final String POST_SUMMARY_PROMPT_BACKUP = """
                                                                你是一个专业的规则制定人员，请根据下列红旗智联app的动态内容（话题+文字+图片+标签）制定一套规则，包括评分的维度以及解释、各项分数和总分阈值
                                                                1.设计方案：
                                                                    - 按总分100分的标准进行设计，除了权重这一项其余每一项的得分之和应为100分
                                                                    - 不要有父子项，一个标准一个得分
                                                                    - 话题为官方发布，话题本身不作为评判标准
                                                                    - 需要考虑内容和话题相关度、图片和内容相关度，若不相关则非优质内容
                                                                    - 提供的只有话题、文字和图片信息，并没有点赞数、评论数量、互动信息等，不要考虑这些因素
                                                                    - 得分阈值高于80分即为优质动态，低于80分则为普通动态
                                                                    - 按照总结的这套规则评分后，提供的每一条动态均符合各自的标签
                                                                2. 必须遵守：
                                                                - 仅基于可见内容判断，禁止猜测或假设
                                                                - 评分原因需包含具体数据
                                                                - 仅返回规则，不要返回具体评分案例
                                                                """;

    public static final String EXTRACT_PROMPT = """
        你是一个车辆参数提取助手，请根据提供的描述提取车系名称以及数字类型的参数名称和值，例如：
        {
        "name":"天工05",
        "续航":"1000km",
        "车长":"5m"
        },若没有提到任何数字类型的车辆参数信息，返回
        {
        "name":null
        }
        """;

    public static final String TRUTH_PROMPT = """
        你是一个参数对比助手，请根据提供的两段车辆参数判断描述是否一致，误差不超过10%均认为一致
        一致则返回"是"，
        不一致则返回"否"。
        注意，不要添加任何额外文本
        """;

    @Autowired
    private AliFeignClient aliFeignClient;

    public String buildCommentRulePrompt(List<ContentReviewResult> rules) {
        StringBuilder ruleDesc = new StringBuilder();
        StringBuilder ruleJson = new StringBuilder();
        int count = rules.size();

        for (ContentReviewResult rule : rules) {
            // 评分项说明
            ruleDesc.append(String.format("- %s：%s\n", rule.getType(), rule.getReason()));
            // JSON格式
            ruleJson.append(String.format("\"%s\": {\"score\": %s, \"reason\": \"%s\"},\n",
                    rule.getType(), "0-"+rule.getScore(), rule.getType() + "分析"));
        }
        // 去掉最后一个逗号
        if (ruleJson.length() > 0) {
            ruleJson.setLength(ruleJson.length() - 2);
        }

        // 拼接到COMMENT_PROMPT
        String prompt = String.format(
                COMMENT_PROMPT,
                COMMENT_PRE, // %s1
                count,       // %s2
                ruleDesc.toString(), // %s3
                "{\n" + ruleJson.toString() + "\n}" // %s4
        );
        return prompt;
    }

    @Override
    public String commentScore(String topic, String content, List<ContentReviewResult> rules) {

        log.info("评论主题 topic: {}, 评论内容 content: {}", topic, content);

        String prompt = buildCommentRulePrompt(rules);
        log.info("评论拼接prompt: {}", prompt);

        // System message
        MessageEntity systemMessage = new MessageEntity();
        systemMessage.setRole("system");
        systemMessage.setContent(List.of(
                Map.of("type", "text", "text", prompt)
        ));

        // User message
        MessageEntity userMessage = new MessageEntity();
        userMessage.setRole("user");

        // 创建包含图片的内容列表
        List<Map<String, Object>> contentList = new ArrayList<>();

        // 添加主题内容
        contentList.add(Map.of("type", "text", "text", "主题:" + topic));

        // 添加评论内容
        contentList.add(Map.of("type", "text", "text", "评论:" + content));

        // 添加固定文本查询 - 根据官方文档，目前模型内部会统一使用"Read all the text in the image."进行识别
//        contentList.add(Map.of("type", "text", "text", "Read all the text in the image."));

        userMessage.setContent(contentList);

        AnalysisRequest request = new AnalysisRequest();
        request.setModel("qwen-max");  // 使用qwen-max模型
        request.setTemperature(0.3f);
        request.setMessages(List.of(systemMessage, userMessage));

        log.info("[CommentReview] request: {}", JSON.toJSONString(request));
        AnalysisResponse response = aliFeignClient.analyze(request);

        log.info("[CommentReview] response: {}", JSON.toJSONString(response));
        if (Objects.nonNull(response) && !CollectionUtils.isEmpty(response.getChoices())) {
            return response.getChoices().get(CommonConstants.FIRST_INDEX).getMessage().getContent();
        }
        return "未得出对应分数";
    }

    public String buildPostRulePrompt(List<ContentReviewResult> rules, boolean isFull) {
        StringBuilder ruleDesc = new StringBuilder();
        StringBuilder ruleJson = new StringBuilder();
        int count = rules.size();

        if (isFull){
            for (ContentReviewResult rule : rules) {
                ruleDesc.append(String.format("- %s：%s\n", rule.getType(), rule.getReason()));
                ruleJson.append(String.format("\"%s\": {\"score\": %s, \"reason\": \"%s\"},\n",
                        rule.getType(), "0-"+rule.getScore(), rule.getType() + "分析"));
            }
        } else {

            for (ContentReviewResult rule : rules) {
                ruleDesc.append(String.format("- %s：%s\n", rule.getType(), rule.getReason()));
                int flooredScore = (int) Math.floor(rule.getScore() * 0.8);
                ruleJson.append(String.format("\"%s\": {\"score\": %s, \"reason\": \"%s\"},\n",
                        rule.getType(), "0-"+flooredScore, rule.getType() + "分析"));
            }
        }

        if (ruleJson.length() > 0) {
            ruleJson.setLength(ruleJson.length() - 2);
        }
        String prompt = String.format(
                POST_PROMPT,
                POST_PRE,
                count,  // 评分项数量
                ruleDesc.toString(), // 评分项说明
                "{\n" + ruleJson.toString() + "\n}" // JSON格式
        );
        return prompt;
    }

    @Override
    public String postScore(String topic, String content, List<String> photoUrls, Boolean flag, List<ContentReviewResult> rules) {
        String prompt = buildPostRulePrompt(rules, flag);
        log.info("评分拼接prompt: {}", prompt);
        // System message
        MessageEntity systemMessage = new MessageEntity();
        systemMessage.setRole("system");
        systemMessage.setContent(List.of(
                Map.of("type", "text", "text", prompt)
        ));

        // User message - 支持多张图片
        MessageEntity userMessage = new MessageEntity();
        userMessage.setRole("user");

        // 创建包含所有图片和文本的内容列表
        List<Map<String, Object>> contentList = new ArrayList<>();

        if (!CollectionUtils.isEmpty(photoUrls)) {
            for (String photoUrl : photoUrls) {
                contentList.add(Map.of(
                        "type", "image_url",
                        "image_url", Map.of("url", photoUrl)
                ));
            }
        }
        contentList.add(Map.of("type", "text", "text", "[话题]：" + topic));
        contentList.add(Map.of("type", "text", "text", "[正文内容]：" + content));
        userMessage.setContent(contentList);

        AnalysisRequest request = new AnalysisRequest();
        request.setModel("qwen-vl-max");
        request.setTemperature(0.3f);
        request.setMessages(List.of(systemMessage, userMessage));

        log.info("[PostReview] request: {}", JSON.toJSONString(request));
        AnalysisResponse response = aliFeignClient.analyze(request);

        log.info("[PostReview] response: {}", JSON.toJSONString(response));
        if (Objects.nonNull(response) && !CollectionUtils.isEmpty(response.getChoices())) {
            return response.getChoices().get(CommonConstants.FIRST_INDEX).getMessage().getContent();
        }
        return "未识别图片";
    }


    public String extractCommentFeature(List<CommentSummaryRequest> requests){
        // System message
        MessageEntity systemMessage = new MessageEntity();
        systemMessage.setRole("system");
        systemMessage.setContent(List.of(
                Map.of("type", "text", "text", COMMENT_FEATURE_PROMPT)
        ));

        // User message
        MessageEntity userMessage = new MessageEntity();
        userMessage.setRole("user");

        // 创建包含图片的内容列表
        List<Map<String, Object>> contentList = new ArrayList<>();

        for (CommentSummaryRequest request : requests) {
            contentList.add(Map.of("type", "text", "text", "主题:" + request.getTopic() + "评论:" + request.getContent() + "标签:" + request.getTag()));
        }

        // 添加固定文本查询 - 根据官方文档，目前模型内部会统一使用"Read all the text in the image."进行识别
//        contentList.add(Map.of("type", "text", "text", "Read all the text in the image."));

        userMessage.setContent(contentList);

        AnalysisRequest request = new AnalysisRequest();
        request.setModel("qwen3-235b-a22b");  // 使用qwen-max模型
        request.setMessages(List.of(systemMessage, userMessage));

        log.info("[CommentSummary] request: {}", JSON.toJSONString(request));
        AnalysisResponse response = aliFeignClient.analyze(request);

        log.info("[CommentSummary] response: {}", JSON.toJSONString(response));
        if (Objects.nonNull(response) && !CollectionUtils.isEmpty(response.getChoices())) {
            return response.getChoices().get(CommonConstants.FIRST_INDEX).getMessage().getContent();
        }
        return "未得出对应分数";
    }
    @Override
    public String commentSummary(List<CommentSummaryRequest> requests, String commentRule) {
        String features = extractCommentFeature(requests);
        log.info("评论特征提取结果:{}", features);
        // System message
        MessageEntity systemMessage = new MessageEntity();
        systemMessage.setRole("system");
        systemMessage.setContent(List.of(
                Map.of("type", "text", "text", COMMENT_SUMMARY_PROMPT)
        ));

        // User message
        MessageEntity userMessage = new MessageEntity();
        userMessage.setRole("user");

        // 创建包含图片的内容列表
        List<Map<String, Object>> contentList = new ArrayList<>();

        contentList.add(Map.of("type", "text", "text", features));
        contentList.add(Map.of("type", "text", "text", "初始版规则：" + commentRule));

        // 添加固定文本查询 - 根据官方文档，目前模型内部会统一使用"Read all the text in the image."进行识别
//        contentList.add(Map.of("type", "text", "text", "Read all the text in the image."));

        userMessage.setContent(contentList);

        AnalysisRequest request = new AnalysisRequest();
        request.setModel("qwen3-235b-a22b");  // 使用qwen-max模型
        request.setMessages(List.of(systemMessage, userMessage));

        log.info("[CommentSummary] request: {}", JSON.toJSONString(request));
        AnalysisResponse response = aliFeignClient.analyze(request);

        log.info("[CommentSummary] response: {}", JSON.toJSONString(response));
        if (Objects.nonNull(response) && !CollectionUtils.isEmpty(response.getChoices())) {
            return response.getChoices().get(CommonConstants.FIRST_INDEX).getMessage().getContent();
        }
        return "未得出对应分数";
    }

    public String extractPostFeature(List<PostSummaryRequest> requests){
        // System message
        MessageEntity systemMessage = new MessageEntity();
        systemMessage.setRole("system");
        systemMessage.setContent(List.of(
                Map.of("type", "text", "text", POST_FEATURE_PROMPT)
        ));

        // User message
        MessageEntity userMessage = new MessageEntity();
        userMessage.setRole("user");

        // 创建包含图片的内容列表
        List<Map<String, Object>> contentList = new ArrayList<>();

        for (PostSummaryRequest request : requests) {
            contentList.add(Map.of("type", "text", "text", "主题:" + request.getTopic() + " 内容:" + request.getContent() + " 标签:" + request.getTag()));
            if (request.getPicUrls() != null) {
                request.getPicUrls().forEach(photoUrl -> {
                    contentList.add(Map.of(
                            "type", "image_url",
                            "image_url", Map.of("url", photoUrl)
                    ));
                });
            }
        }
        userMessage.setContent(contentList);

        AnalysisRequest request = new AnalysisRequest();
        request.setModel("qwen-vl-max");  // 使用qwen-vl-max模型
        request.setMessages(List.of(systemMessage, userMessage));

        log.info("[CommentSummary] request: {}", JSON.toJSONString(request));
        AnalysisResponse response = aliFeignClient.analyze(request);

        log.info("[CommentSummary] response: {}", JSON.toJSONString(response));
        if (Objects.nonNull(response) && !CollectionUtils.isEmpty(response.getChoices())) {
            return response.getChoices().get(CommonConstants.FIRST_INDEX).getMessage().getContent();
        }
        return "未得出对应分数";
    }

    @Override
    public String postSummary(List<PostSummaryRequest> requests, String postRule) {
        String features = extractPostFeature(requests);
        // System message
        MessageEntity systemMessage = new MessageEntity();
        systemMessage.setRole("system");
        systemMessage.setContent(List.of(
                Map.of("type", "text", "text", POST_SUMMARY_PROMPT)
        ));

        // User message - 支持多张图片
        MessageEntity userMessage = new MessageEntity();
        userMessage.setRole("user");

        List<Map<String, Object>> contentList = new ArrayList<>();
        contentList.add(Map.of("type", "text", "text", features));
        contentList.add(Map.of("type", "text", "text", "初始版规则：" + postRule));

        userMessage.setContent(contentList);

        AnalysisRequest request = new AnalysisRequest();
        request.setModel("qwen3-235b-a22b");
        request.setMessages(List.of(systemMessage, userMessage));

        log.info("[PostSummary] request: {}", JSON.toJSONString(request));
        AnalysisResponse response = aliFeignClient.analyze(request);

        log.info("[PostSummary] response: {}", JSON.toJSONString(response));
        if (Objects.nonNull(response) && !CollectionUtils.isEmpty(response.getChoices())) {
            return response.getChoices().get(CommonConstants.FIRST_INDEX).getMessage().getContent();
        }
        return "未识别图片";
    }

    @Override
    public String truthValidate(String content, String query) {
        // System message
        MessageEntity systemMessage = new MessageEntity();
        systemMessage.setRole("system");
        systemMessage.setContent(List.of(
                Map.of("type", "text", "text", TRUTH_PROMPT)
        ));

        // User message
        MessageEntity userMessage = new MessageEntity();
        userMessage.setRole("user");

        // 创建包含图片的内容列表
        List<Map<String, Object>> contentList = new ArrayList<>();

        contentList.add(Map.of("type", "text", "text", content));
        contentList.add(Map.of("type", "text", "text", query));
        // 添加固定文本查询 - 根据官方文档，目前模型内部会统一使用"Read all the text in the image."进行识别
//        contentList.add(Map.of("type", "text", "text", "Read all the text in the image."));

        userMessage.setContent(contentList);

        AnalysisRequest request = new AnalysisRequest();
        request.setModel("qwen-max");  // 使用qwen-max模型
        request.setMessages(List.of(systemMessage, userMessage));

        log.info("[CommentSummaryAll] request: {}", JSON.toJSONString(request));
        AnalysisResponse response = aliFeignClient.analyze(request);

        log.info("[CommentSummaryAll] response: {}", JSON.toJSONString(response));
        if (Objects.nonNull(response) && !CollectionUtils.isEmpty(response.getChoices())) {
            return response.getChoices().get(CommonConstants.FIRST_INDEX).getMessage().getContent();
        }
        return "未得出对应分数";
    }

    @Override
    public String extractVehicleParam(String content) {
        // System message
        MessageEntity systemMessage = new MessageEntity();
        systemMessage.setRole("system");
        systemMessage.setContent(List.of(
                Map.of("type", "text", "text", EXTRACT_PROMPT)
        ));

        // User message
        MessageEntity userMessage = new MessageEntity();
        userMessage.setRole("user");

        // 创建包含图片的内容列表
        List<Map<String, Object>> contentList = new ArrayList<>();

        contentList.add(Map.of("type", "text", "text", content));
        // 添加固定文本查询 - 根据官方文档，目前模型内部会统一使用"Read all the text in the image."进行识别
//        contentList.add(Map.of("type", "text", "text", "Read all the text in the image."));

        userMessage.setContent(contentList);

        AnalysisRequest request = new AnalysisRequest();
        request.setModel("qwen-max");  // 使用qwen-max模型
        request.setMessages(List.of(systemMessage, userMessage));

        log.info("[CommentSummaryAll] request: {}", JSON.toJSONString(request));
        AnalysisResponse response = aliFeignClient.analyze(request);

        log.info("[CommentSummaryAll] response: {}", JSON.toJSONString(response));
        if (Objects.nonNull(response) && !CollectionUtils.isEmpty(response.getChoices())) {
            return response.getChoices().get(CommonConstants.FIRST_INDEX).getMessage().getContent();
        }
        return "提取失败";
    }
}
