package com.faw.work.ais.aic.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 内容搜索请求
 *
 * <AUTHOR> Assistant
 */
@Data
@Schema(description = "内容搜索请求")
public class SearchContentRequest {

    @Schema(description = "知识库ID")
    @NotNull(message = "知识库ID不能为空")
    private Long ragKnowledgeId;

    @Schema(description = "查询内容")
    @NotBlank(message = "查询内容不能为空")
    private String query;


    @Schema(description = "历史信息")
    private String chatHistory;

    @Schema(description = "标签属性：车系信息")
    @NotBlank(message = "标签属性信息不能为空")
    private String label;


} 