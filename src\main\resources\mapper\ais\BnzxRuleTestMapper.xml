<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.faw.work.ais.mapper.ais.BnzxRuleTestMapper">

    <resultMap id="BaseResultMap" type="com.faw.work.ais.model.BnzxRuleTest">
            <result property="filedesc" column="fileDesc" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="VARCHAR"/>
            <result property="vin" column="vin" jdbcType="VARCHAR"/>
            <result property="indicate" column="indicate" jdbcType="VARCHAR"/>
            <result property="drivedate" column="driveDate" jdbcType="DATE"/>
            <result property="carliscens" column="carLiscens" jdbcType="VARCHAR"/>
            <result property="cuscode" column="cusCode" jdbcType="VARCHAR"/>
            <result property="cusname" column="cusName" jdbcType="VARCHAR"/>
            <result property="driveno" column="driveno" jdbcType="VARCHAR"/>
            <result property="picurl" column="picUrl" jdbcType="VARCHAR"/>
            <result property="caruse" column="carUse" jdbcType="VARCHAR"/>
            <result property="saletype" column="saleType" jdbcType="VARCHAR"/>
            <result property="custype" column="cusType" jdbcType="VARCHAR"/>
            <result property="price" column="price" jdbcType="DECIMAL"/>
            <result property="indicatedate" column="indicatedate" jdbcType="DATE"/>
            <result property="carcolor" column="carcolor" jdbcType="VARCHAR"/>
            <result property="carunit" column="carUnit" jdbcType="VARCHAR"/>
            <result property="address" column="address" jdbcType="VARCHAR"/>
            <result property="idcard" column="idCard" jdbcType="VARCHAR"/>
            <result property="shxycode" column="shxycode" jdbcType="VARCHAR"/>
            <result property="airesult" column="aiResult" jdbcType="VARCHAR"/>
            <result property="rawResult" column="raw_result" jdbcType="VARCHAR"/>
            <result property="taskType" column="task_type" jdbcType="VARCHAR"/>
            <result property="id" column="id" jdbcType="VARCHAR"/>
            <result property="updatetime" column="updatetime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        fileDesc,vin,price,
        indicate,indicatedate,saleType,
        carUse,carcolor,carUnit,
        address,driveDate,carLiscens,
        idCard,shxycode,cusType,
        driveno,picUrl,aiResult,raw_result,id,task_type,status,cusCode,cusName,updatetime
    </sql>
</mapper>
