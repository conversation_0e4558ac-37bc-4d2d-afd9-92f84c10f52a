package com.faw.work.ais.aic.service.impl;

import com.faw.work.ais.aic.model.request.RagDocumentProcessAllRequest;
import com.faw.work.ais.aic.model.request.SearchContentRequest;
import com.faw.work.ais.aic.model.response.RagDocumentProcessAllResponse;
import com.faw.work.ais.aic.model.response.SimilarContentSearchResponse;
import com.faw.work.ais.aic.service.RagDocumentService;
import org.springframework.mock.web.MockMultipartFile;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

/**
 * RagDocumentService测试类
 *
 * <AUTHOR>
 */
@SpringBootTest
public class RagDocumentServiceImplTest {

    @Autowired
    private RagDocumentService ragDocumentService;

    @Test
    public void testSearchSimilarContentNew() {
        // 创建测试请求
        SearchContentRequest request = new SearchContentRequest();
        request.setRagKnowledgeId(1L);
        request.setQuery("测试查询内容");
        request.setLabel("测试标签");

        try {
            // 调用新的搜索方法
            List<SimilarContentSearchResponse> results = ragDocumentService.searchSimilarContentNew(request);
            
            // 验证结果
            System.out.println("搜索结果数量: " + results.size());
            for (SimilarContentSearchResponse result : results) {
                System.out.println("ID: " + result.getId());
                System.out.println("文档ID: " + result.getDocumentId());
                System.out.println("标签: " + result.getLabel());
                System.out.println("得分: " + result.getScore());
                System.out.println("内容: " + result.getDocumentContent());
                System.out.println("---");
            }
        } catch (Exception e) {
            System.out.println("测试异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testProcessDocumentAll() {
        // 注意：这是一个示例测试方法，实际使用时需要：
        // 1. 准备真实的测试文件
        // 2. 配置测试数据库
        // 3. 确保相关服务正常运行

        System.out.println("文档一体化处理测试准备完成");
        System.out.println("实际测试需要配置以下内容：");
        System.out.println("1. 有效的categoryId和ragKnowledgeId");
        System.out.println("2. 测试文件（PDF、DOC、DOCX等）");
        System.out.println("3. 文件存储服务配置");
        System.out.println("4. Milvus向量数据库配置");
        System.out.println("5. 嵌入模型服务配置");

        // 示例代码（需要真实环境才能运行）：
        /*
        try {
            // 创建模拟文件
            MockMultipartFile mockFile = new MockMultipartFile(
                "file",
                "test.txt",
                "text/plain",
                "这是一个测试文档内容".getBytes()
            );

            // 创建请求
            RagDocumentProcessAllRequest request = new RagDocumentProcessAllRequest();
            request.setCategoryId(1L);
            request.setRagKnowledgeId(1L);
            request.setFile(mockFile);
            request.setChunkStrategy("00");
            request.setChunkLength(500);
            request.setOverlapLength(50);
            request.setLabel("测试标签");

            // 执行一体化处理
            RagDocumentProcessAllResponse response = ragDocumentService.processDocumentAll(request);

            // 验证结果
            System.out.println("处理结果:");
            System.out.println("文档ID: " + response.getDocumentId());
            System.out.println("文档名称: " + response.getDocumentName());
            System.out.println("知识库ID: " + response.getRagKnowledgeId());
            System.out.println("知识库名称: " + response.getKnowledgeName());
            System.out.println("上传状态: " + response.getUploadSuccess());
            System.out.println("切分状态: " + response.getSplitSuccess());
            System.out.println("绑定状态: " + response.getBindSuccess());
            System.out.println("向量化状态: " + response.getVectorSuccess());
            System.out.println("切分数量: " + response.getSplitCount());
            System.out.println("状态描述: " + response.getStatusMessage());

        } catch (Exception e) {
            System.out.println("测试异常: " + e.getMessage());
            e.printStackTrace();
        }
        */
    }
}
