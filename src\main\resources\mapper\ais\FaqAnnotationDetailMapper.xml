<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.faw.work.ais.aic.mapper.faq.FaqAnnotationDetailMapper">

    <!-- 根据任务ID分页查询标注详情 -->
    <select id="findByTaskId" resultType="com.faw.work.ais.aic.model.domain.FaqAnnotationDetailPO">
        SELECT *
        FROM faq_annotation_detail
        WHERE task_id = #{taskId}
        <if test="annotationType != null and annotationType != ''">
            AND annotation_type = #{annotationType}
        </if>
        <if test="isLocked != null">
            AND is_locked = #{isLocked}
        </if>
        ORDER BY created_at DESC
    </select>


    <!-- 统计任务的总数据量 -->
    <select id="countByTaskId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM faq_annotation_detail
        WHERE task_id = #{taskId}
    </select>

    <!-- 统计任务的已标注数量 -->
    <select id="countAnnotatedByTaskId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM faq_annotation_detail
        WHERE task_id = #{taskId}
        AND is_locked = 1
    </select>

    <!-- 根据标注类型统计数量 -->
    <select id="countByAnnotationType" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM faq_annotation_detail
        WHERE task_id = #{taskId}
        <if test="annotationType != null and annotationType != ''">
            AND annotation_type = #{annotationType}
        </if>
        <if test="annotationSubtype != null and annotationSubtype != ''">
            AND annotation_subtype = #{annotationSubtype}
        </if>
    </select>

    <!-- 更新标注信息 -->
    <update id="updateAnnotation">
        UPDATE faq_annotation_detail
        SET annotation_type = #{annotationType},
            annotation_subtype = #{annotationSubtype},
            annotator_id = #{annotatorId},
            annotator_name = #{annotatorName},
            annotated_at = NOW(),
            is_locked = 1,
            updated_at = NOW()
        WHERE id = #{id}
    </update>

    <!-- 解锁标注（重新标注） -->
    <update id="unlockAnnotation">
        UPDATE faq_annotation_detail
        SET annotation_type = NULL,
            annotation_subtype = NULL,
            annotator_id = NULL,
            annotator_name = NULL,
            annotated_at = NULL,
            is_locked = 0,
            updated_at = NOW()
        WHERE id = #{id}
    </update>

    <!-- 根据任务ID查询所有标注数据 -->
    <select id="findAllByTaskId" resultType="com.faw.work.ais.aic.model.domain.FaqAnnotationDetailPO">
        SELECT *
        FROM faq_annotation_detail
        WHERE task_id = #{taskId}
        ORDER BY created_at DESC
    </select>
    <select id="selectMarkedCountByTaskId" resultType="java.lang.Integer">
        select count(*) from faq_annotation_detail where task_id = #{taskId} and is_locked = 1
    </select>

    <!-- 批量插入标注详情 -->
    <insert id="batchInsert">
        INSERT INTO faq_annotation_detail (
            id, task_id, hit_log_id, user_question, match_type, matched_content,
            knowledge_id, faq_title, match_score, is_locked, created_at, updated_at
        ) VALUES
        <foreach collection="details" item="detail" separator=",">
            (
                #{detail.id}, #{detail.taskId}, #{detail.hitLogId}, #{detail.userQuestion},
                #{detail.matchType}, #{detail.matchedContent}, #{detail.knowledgeId},
                #{detail.faqTitle}, #{detail.matchScore}, #{detail.isLocked},
                #{detail.createdAt}, #{detail.updatedAt}
            )
        </foreach>
    </insert>

</mapper>
