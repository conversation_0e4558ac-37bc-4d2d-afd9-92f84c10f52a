package com.faw.work.ais.entity.vo.ai;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 看板的列表数据
 */
@Data
//public class KanBanRuleListVO extends KanBanBillListVO{
public class KanBanRuleListVO {

    @ApiModelProperty("每次AI请求的id")
    private String traceId;

    @ApiModelProperty("规则名称")
    private String ruleName;

    @ApiModelProperty("规则Id")
    private String ruleId;

    @ApiModelProperty("规则材料链接-待定这里建议一个新接口，只显示一个按钮，把tranceId和batchId传过去查")
    private String ruleFileUrl;

    @ApiModelProperty("规则AI审核状态;0-驳回；1-通过；")
    private String ruleAiCheckStatus;

    @ApiModelProperty("规则人工审核状态；0-驳回；1-通过")
    private String ruleHumanCheckStatus;

    @ApiModelProperty("AI审核原因")
    private String aiCheckReason;

    @ApiModelProperty("人工审核原因")
    private String humanCheckReason;

    @ApiModelProperty("AI审核时间")
    private String aiCheckTime;

    @ApiModelProperty("人工审核时间")
    private String humanCheckTime;

}
