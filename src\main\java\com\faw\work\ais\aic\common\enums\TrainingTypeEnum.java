package com.faw.work.ais.aic.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 培训类型枚举
 *
 * <AUTHOR>
 * @date 2025/06/29
 */
@Getter
@AllArgsConstructor
public enum TrainingTypeEnum {
    /**
     * 售前培训
     */
    PRE_SALES_TRAINING("20", "售前培训"),

    /**
     * 竞品话术
     */
    COMPETITOR_SPEECH("48", "竞品话术");

    private final String code;
    private final String desc;

    public static String getByCode(String code) {
        for (TrainingTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return null;
    }
}