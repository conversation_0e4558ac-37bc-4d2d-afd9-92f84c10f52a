package com.faw.work.ais.common.dto.chat;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

@Data
public class RecommendRequest {

    /**
     * 机器人id
     */
    @NotBlank(message = "机器人id不能为空")
    private String robotId;

    /**
     * 用户id
     */
    @NotBlank(message = "用户id不能为空")
    private String userId;

    /**
     * 角色(车主1， 粉丝0)
     */
    private String role;

    /**
     * 车系
     */
    private String series;

    /**
     * 标签
     */
    private List<String> label;

    /**
     * 历史问题列表
     */
    private List<String> historyQuestions;

    /**
     * 排除的问题id列表
     */
    private List<String> excludeIds;

}
