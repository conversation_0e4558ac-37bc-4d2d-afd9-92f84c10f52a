package com.faw.work.ais.service.tool;

import com.alibaba.fastjson.JSON;
import com.faw.work.ais.aic.common.util.RedisService;
import com.faw.work.ais.aic.model.request.FaqSearchByRobotRequest;
import com.faw.work.ais.aic.model.response.FaqKnowledgeResponse;
import com.faw.work.ais.aic.service.FaqKnowledgeService;
import com.faw.work.ais.common.CommonConstants;
import com.faw.work.ais.common.dto.chat.*;
import com.faw.work.ais.common.enums.chat.ChatToolEnum;
import com.faw.work.ais.config.chat.ThreadContext;
import com.faw.work.ais.feign.chat.AppFeignClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ai.chat.model.ToolContext;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * 车辆服务类，用于处理车辆相关的业务逻辑
 *
 * <AUTHOR>
 * @since 2025-05-29 15:54
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class VehicleService {

    private final RedisService redisService;

    private final AppFeignClient appFeignClient;

    private final FaqKnowledgeService faqKnowledgeService;

    private final FunctionService functionService;


    private static final String ENV = "prod";

    private static final String ROBOT_ID = "6ec8fa5c1b65d7825ff1449f9776d66e";

    private static final int TOP_K = 1;

    private static final float SIMILARITY_THRESHOLD = 0.9f;

    private static final String APP_SUCCESS_FLAG = "000000";

    private static final String ERROR_MESSAGE = """
            预约失败，请联系系统管理员
            """;

    private static final String PICK_UP_CAR_SUCCESS_MESSAGE = """
            您可以通过以下路径预约上门取车服务：
            路径：首页 > 用车服务 > 预约保养
            
            我们也为您准备了快速入口 ↓
            """;

    private static final String MAINTENANCE_SUCCESS_MESSAGE = """
            「好的，我来帮您安排一次维保服务」
            
            #### 根据系统识别您的用车信息：
            - **车辆**：红旗 %s（车牌：%s）
            - **最近服务门店**：%s（上次服务时间%s）
            - **推荐门店**：%s
            - **推荐时间**：%s（剩余%s个工位）
            
            #### 推荐理由：
            - 系统检测您上次也是在该门店保养，服务记录良好
            - 当前该店%s有空余工位，支持快速预约
            
            #### 请确认以下信息，点击一键预约也可修改后再提交：
            - **预约项目**：保养
            - **门店**：%s
            - **预约时间**：%s
            - **车型**：红旗 %s
            - **联系方式**：%s
            """;

    private static final String SERVICE_FAIL_MESSAGE = """
            「抱歉，我无法帮您预约%s服务」
            
            #### 失败原因如下：
            %s
            """;

    private static final String THINK_FAIL_MESSAGE = """
            未找到匹配的答案
            """;


    @Tool(name = "pickUpCar", returnDirect = true, description = "预约上门取车方法：帮助用户预约预约上门取车服务")
    public String pickUpCar(@ToolParam(description = "取车时间，格式要求为 yyyy-MM-dd") String time,
                            ToolContext toolContext) {
        log.info("[VehicleService][pickUpCar][entrance] time: {}, toolContext: {}", time, JSON.toJSONString(toolContext));

        // 1 send request
        UserInfo userInfo = (UserInfo) toolContext.getContext().get(CommonConstants.KEY_USER_INFO);
        AppResponse<AppEntity> appResponse =
                appFeignClient.orderPickUp(AppRequest.builder().aid(userInfo.getUserCode()).build());
        log.info("[VehicleService][pickUpCar] appResponse: {}", JSON.toJSONString(appResponse));

        // 2 deal with response
        String message;
        ToolCacheEntity toolCache = ToolCacheEntity.builder().toolName(ChatToolEnum.PICK_UP_CAR.getName()).toolStatus(false).build();
        if (Objects.isNull(appResponse)) {
            message = ERROR_MESSAGE;
        } else if (APP_SUCCESS_FLAG.equals(appResponse.getCode())) {
            toolCache.setToolStatus(true);
            toolCache.setToolValue(AppEntity.builder().vin(userInfo.getVin()).appointmentTime(time).build());

            message = PICK_UP_CAR_SUCCESS_MESSAGE;
        } else {
            message = String.format(SERVICE_FAIL_MESSAGE, "上门取车", this.buildMsg(appResponse.getMsg()));
        }

        // 3 save cache
        redisService.set((String) toolContext.getContext().get(CommonConstants.KEY_CACHE_ID), JSON.toJSONString(toolCache), CommonConstants.SECONDS_ONE_HOUR);

        return message;
    }

    @Tool(name = "maintenance", returnDirect = true, description = "预约维修保养方法：帮助用户预约维修保养服务")
    public String maintenance(ToolContext toolContext) {
        log.info("[VehicleService][maintenance][entrance] toolContext: {}", JSON.toJSONString(toolContext));

        // 1 send request
        UserInfo userInfo = (UserInfo) toolContext.getContext().get(CommonConstants.KEY_USER_INFO);
        AppResponse<AppEntity> appResponse = appFeignClient.orderMaintenance(
                AppRequest.builder().aid(userInfo.getUserCode()).cityCode(userInfo.getCityCode())
                        .lat(userInfo.getLat()).lon(userInfo.getLon()).vin(userInfo.getVin()).build());
        log.info("[VehicleService][maintenance] appResponse: {}", JSON.toJSONString(appResponse));

        // 2 deal with response
        String message;
        ToolCacheEntity toolCache = ToolCacheEntity.builder().toolName(ChatToolEnum.MAINTENANCE.getName()).toolStatus(false).build();

        String thoughtChain = ThreadContext.get();
        if (StringUtils.isNotEmpty(thoughtChain)) {
            toolCache.setThoughtChain(thoughtChain);
            ThreadContext.clear();
        }

        if (Objects.isNull(appResponse)) {
            message = ERROR_MESSAGE;
        } else if (APP_SUCCESS_FLAG.equals(appResponse.getCode())) {
            toolCache.setToolStatus(true);
            toolCache.setToolValue(appResponse.getData());

            AppEntity data = appResponse.getData();
            String appointmentTime = String.join(CommonConstants.BLANK, data.getAppointmentDate(), data.getAppointmentDateTime());
            if (StringUtils.isEmpty(data.getLastEnterDealerName())) {
                data.setLastEnterDealerName("无最近服务门店");
                data.setLastEnterTime2("未知");
            }

            message = String.format(MAINTENANCE_SUCCESS_MESSAGE, data.getCarSeries(), data.getVehicleNumber(),
                    data.getLastEnterDealerName(), data.getLastEnterTime2(), data.getDealerName(), appointmentTime,
                    data.getNumber(), appointmentTime, data.getDealerName(), appointmentTime, data.getCarSeries(), userInfo.getPhone());
        } else {
            message = String.format(SERVICE_FAIL_MESSAGE, "维修保养", this.buildMsg(appResponse.getMsg()));
        }

        // 3 save cache
        redisService.set((String) toolContext.getContext().get(CommonConstants.KEY_CACHE_ID), JSON.toJSONString(toolCache), CommonConstants.SECONDS_ONE_HOUR);

        return message;
    }

    /**
     * 构建消息
     *
     * @param msg 消息
     * @return 构建后的消息
     */
    private String buildMsg(String msg) {
        log.info("[VehicleService][buildMsg][entrance] msg: {}", msg);
        if (StringUtils.isEmpty(msg)) {
            return ERROR_MESSAGE;
        }

        try {
            List<String> msgList = JSON.parseArray(msg, String.class);

            StringBuilder result = new StringBuilder();
            for (String s : msgList) {
                result.append("- ").append(s).append("\n");
            }

            return result.toString();
        } catch (Exception e) {
            log.warn("[VehicleService][buildMsg][error] ", e);
            return msg;
        }
    }

    @Tool(name = "vehicleFault", returnDirect = true, description = "检修车辆故障方法：帮助用户识别车辆故障")
    public String vehicleFault(@ToolParam(description = "当前用户问题") String userQuestion,
                               ToolContext toolContext) {
        log.info("[VehicleService][vehicleFault][entrance] userQuestion: {}, toolContext: {}", userQuestion, JSON.toJSONString(toolContext));

        return this.buildFaqMsg(userQuestion, toolContext);
    }

    @Tool(name = "repairProgress", returnDirect = true, description = "查询维修进度方法：帮助用户查询车辆维修进度")
    public String repairProgress(@ToolParam(description = "当前用户问题") String userQuestion,
                                 ToolContext toolContext) {
        log.info("[VehicleService][repairProgress][entrance] userQuestion: {}, toolContext: {}", userQuestion, JSON.toJSONString(toolContext));

        return this.buildFaqMsg(userQuestion, toolContext);
    }

    /**
     * 构建faq消息
     *
     * @param question 问题
     * @param toolContext 工具上下文
     * @return 构建后的消息
     */
    private String buildFaqMsg(String question, ToolContext toolContext) {

        FaqSearchByRobotRequest faqRequest = FaqSearchByRobotRequest.of(ROBOT_ID, question, TOP_K, SIMILARITY_THRESHOLD, ENV, null, null);
        List<FaqKnowledgeResponse> faqList = faqKnowledgeService.searchByRobotId(faqRequest);
        log.info("[VehicleService][buildFaqMsg] faqList: {}", JSON.toJSONString(faqList));
        if (CollectionUtils.isEmpty(faqList)) {
            return THINK_FAIL_MESSAGE;
        }

        FaqAnswerEntity answer = JSON.parseObject(faqList.get(CommonConstants.FIRST_INDEX).getAnswer(), FaqAnswerEntity.class);
        ThreadContext.set(answer.getThink());

        if (ChatToolEnum.MAINTENANCE.getName().equals(answer.getType())) {
            String maintenance = this.maintenance(toolContext);
            log.info("[VehicleService][buildFaqMsg] maintenance: {}", maintenance);
        } else if (ChatToolEnum.STAFF_SERVICE.getName().equals(answer.getType())) {
            String staffService = functionService.staffService(question, toolContext);
            log.info("[VehicleService][buildFaqMsg] staffService: {}", staffService);
        }

        return answer.getContent();
    }

}
