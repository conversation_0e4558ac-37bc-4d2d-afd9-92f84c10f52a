package com.faw.work.ais.service;

import com.faw.work.ais.common.dto.DogResponse;
import com.faw.work.ais.entity.dto.OpsPromptGenerationDto;
import com.faw.work.ais.model.PromptTemplate;

import java.util.List;

/**
 * 养狗服务接口
 *
 * <AUTHOR>
 */
public interface YangGouService {

    /**
     * 策略生成
     *
     * @param callText
     * @return {@link DogResponse }
     */
    DogResponse generate(String callText);

    /**
     * 更新模板
     *
     * @param opsPromptGenerationDtoList
     * @return {@link String }
     */
    void updateTemplate(List<OpsPromptGenerationDto> opsPromptGenerationDtoList);

    /**
     * 获取最新提示模板
     *
     * @return {@link PromptTemplate }
     */
    PromptTemplate getLatestPromptTemplate();
}
