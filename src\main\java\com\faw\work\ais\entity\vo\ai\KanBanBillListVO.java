package com.faw.work.ais.entity.vo.ai;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 看板列表-单据
 */
@Data
public class KanBanBillListVO {

    @ApiModelProperty("项目")
    private String sysTemName;

    @ApiModelProperty("系统id")
    private String sysTemId;

    @ApiModelProperty("业务ID")
    private String bizId;

    @ApiModelProperty("一整单单据id")
    private String batchId;

    @ApiModelProperty("单据AI审核状态;0-驳回；1-通过")
    private String aiCheckStatus;

    @ApiModelProperty("单据人工审核状态；0-驳回；1-通过")
    private String humanCheckStatus;

    @ApiModelProperty("看板规则明细列")
    List<KanBanRuleListVO> ruleListVOList;

}
