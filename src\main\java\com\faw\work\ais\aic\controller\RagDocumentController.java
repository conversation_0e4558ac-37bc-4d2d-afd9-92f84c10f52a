package com.faw.work.ais.aic.controller;

import cn.hutool.core.collection.CollUtil;
import com.faw.work.ais.aic.model.domain.RagDocumentPO;
import com.faw.work.ais.aic.model.domain.RagKnowledgePO;
import com.faw.work.ais.aic.model.domain.RagKnowledgeDocumentJoinsPO;
import com.faw.work.ais.aic.model.request.*;
import com.faw.work.ais.aic.model.response.RagDocumentProcessAllResponse;
import com.faw.work.ais.aic.model.response.RagKnowledgeDocumentBindResponse;
import com.faw.work.ais.aic.model.response.SimilarContentSearchResponse;
import com.faw.work.ais.aic.service.RagDocumentService;
import com.faw.work.ais.aic.service.RagDocumentSplitService;
import com.faw.work.ais.aic.service.RagKnowledgeDocumentJoinsService;
import com.faw.work.ais.aic.service.RagKnowledgeService;
import com.faw.work.ais.common.Response;
import com.faw.work.ais.common.exception.BizException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * 文档表 控制器
 *
 * <AUTHOR> Assistant
 */
@Tag(name = "文档管理")
@RestController
@Slf4j
@RequestMapping("/rag-document")
public class RagDocumentController {

    @Autowired
    private  RagDocumentService ragDocumentService;
    @Autowired
    private RagDocumentSplitService ragDocumentSplitService;

    @Autowired
    private RagKnowledgeDocumentJoinsService ragKnowledgeDocumentJoinsService;

    @Autowired
    private RagKnowledgeService ragKnowledgeService;

    @Operation(summary = "1.上传知识文档")
    @PostMapping("/upload")
    public Response<RagDocumentPO> upload(@Valid @ModelAttribute RagDocumentAddRequest request) {
        RagDocumentPO document = ragDocumentService.upload(request);
        return Response.success(document);
    }


    @Operation(summary = "下载文档")
    @GetMapping("/download/{documentId}")
    public void download(@PathVariable Long documentId, HttpServletResponse response) {
        log.info("接收到文档下载请求: documentId={}", documentId);
        ragDocumentService.download(documentId, response);

    }

    @Operation(summary = "2.文档切分")
    @PostMapping("/splitDocument")
    public Response<String> splitDocument(@RequestParam("documentId") Long documentId) {
        ragDocumentService.splitDocument(documentId);
        return Response.success("文档切分处理成功");
    }


    @Operation(summary = "3.绑定知识库和文档关系")
    @PostMapping("/bind-documents")
    public Response<RagKnowledgeDocumentBindResponse> bindDocuments(@Valid @RequestBody RagKnowledgeDocumentBindRequest request) {
        log.info("绑定知识库文档关系请求: {}", request);

        try {
            RagKnowledgeDocumentBindResponse response = ragKnowledgeDocumentJoinsService.bindDocuments(request);

            if (response.getFailCount() > 0) {
                log.warn("部分文档绑定失败: 成功={}, 失败={}", response.getSuccessCount(), response.getFailCount());
            }

            return Response.success(response);

        } catch (Exception e) {
            log.error("绑定知识库文档关系失败", e);
            return Response.fail("绑定失败: " + e.getMessage());
        }
    }


    @Operation(summary = "4.向量化文档片段")
    @PostMapping("/vectorDocumentSplits")
    public Response<String> vectorDocumentSplits(@RequestParam("documentId") Long documentId) {
        boolean data = ragDocumentService.vectorDocumentSplits(documentId);
        if (data){
            return Response.success("向量化文档片段成功");
        }
        return Response.fail("向量化文档片段失败");
    }

    @Operation(summary = "单纯删除文档向量索引")
    @PostMapping("/deleteVectors")
    public Response<Long> deleteVectors(@RequestParam("documentId") Long documentId) {
        return Response.success(ragDocumentService.deleteDocumentVectors(documentId));
    }

    @Operation(summary = "删除文档切片信息")
    @PostMapping("/deleteSplits")
    public Response<Boolean> deleteSplits(@RequestParam("documentId") Long documentId) {
        return Response.success(ragDocumentService.deleteDocumentSplits(documentId));
    }


    @Operation(summary = "核心接口:召回非结构化文档相似内容-新版本")
    @PostMapping("/search-similar-content-new")
    public Response<List<SimilarContentSearchResponse>> searchSimilarContentNew(@RequestBody SearchContentRequest request) {
        List<SimilarContentSearchResponse> results = ragDocumentService.searchSimilarContentNew(request);
        return Response.success(results);
    }


    @Operation(summary = "旗宝-同步知识中心文档（获取文档列表+过滤新文档+批量处理）")
    @PostMapping("/sync-knowledge-documents")
    public Response<String> syncKnowledgeDocuments(@Valid @RequestBody SyncKnowledgeDocumentsRequest request) {
        String response = ragDocumentService.syncKnowledgeDocuments(request);
        return Response.success(response);
    }



    @Operation(summary = "非结构化文档一体化处理（基于URL：保存文档+分片+绑定知识库+向量化）")
    @PostMapping("/process-all")
    public Response<List<RagDocumentProcessAllResponse>> processDocumentAll(@Valid @RequestBody List<RagDocumentProcessByUrlRequest> requestList) {
        List<RagDocumentProcessAllResponse> response = new ArrayList<>();
        for (RagDocumentProcessByUrlRequest request : requestList) {
            log.info("接收到基于URL的文档一体化处理请求: categoryId={}, ragKnowledgeId={}, fileUrl={}",
                    request.getCategoryId(), request.getRagKnowledgeId(), request.getFileUrl());

            RagDocumentProcessAllResponse temp = ragDocumentService.processDocumentByUrl(request);
            response.add(temp);
        }

        return Response.success(response);
    }



    @Operation(summary = "数据初始化：根据知识库ID处理未解析的文档(基于excel导入Mysql后进行操作)")
    @PostMapping("/process-unprocessed-documents")
    public Response<String> processUnprocessedDocuments(@RequestParam("ragKnowledgeId") Long ragKnowledgeId) {
        log.info("开始处理知识库未解析文档: ragKnowledgeId={}", ragKnowledgeId);

        // 1. 校验知识库是否存在
        RagKnowledgePO knowledge = ragKnowledgeService.getById(ragKnowledgeId);
        if (knowledge == null) {
            return Response.fail("知识库不存在: ragKnowledgeId=" + ragKnowledgeId);
        }

        // 2. 获取该知识库下所有文档关联
        List<RagKnowledgeDocumentJoinsPO> joins = ragKnowledgeDocumentJoinsService.getByBaseId(ragKnowledgeId);
        if (CollUtil.isEmpty(joins)) {
            return Response.success("该知识库下没有关联的文档");
        }

        // 3. 获取所有关联的文档ID
        List<Long> documentIds = joins.stream()
                .map(RagKnowledgeDocumentJoinsPO::getDocumentId)
                .toList();

        // 4. 查询文档信息
        List<RagDocumentPO> documents = ragDocumentService.listByIds(documentIds);
        if (CollUtil.isEmpty(documents)) {
            return Response.success("未找到关联的文档");
        }

        // 5. 过滤出未解析的文档
        List<RagDocumentPO> unprocessedDocuments = documents.stream()
                .filter(doc -> "00".equals(doc.getParseStatus()))
                .toList();

        if (CollUtil.isEmpty(unprocessedDocuments)) {
            return Response.success("没有需要处理的未解析文档");
        }

        log.info("找到{}个未解析文档需要处理", unprocessedDocuments.size());

        // 6. 处理每个未解析的文档
        int successCount = 0;
        int failCount = 0;
        StringBuilder resultMessage = new StringBuilder();

        for (RagDocumentPO document : unprocessedDocuments) {
            try {
                // 6.1 文档解析和切分
                log.info("开始解析文档: documentId={}, fileName={}", document.getId(), document.getFileName());
                ragDocumentService.splitDocument(document.getId());
                log.info("文档解析切分成功: documentId={}", document.getId());

                // 6.2 向量化
                log.info("开始向量化: documentId={}", document.getId());
                boolean vectorResult = ragDocumentService.vectorDocumentSplits(document.getId());
                if (!vectorResult) {
                    throw new BizException("向量化失败");
                }
                log.info("向量化成功: documentId={}", document.getId());

                successCount++;
            } catch (Exception e) {
                log.error("文档处理失败: documentId={}, fileName={}", document.getId(), document.getFileName(), e);
                failCount++;
                resultMessage.append("文档[").append(document.getFileName()).append("]处理失败: ")
                        .append(e.getMessage()).append("\n");
            }
        }

        String finalResult = String.format("处理完成。成功: %d, 失败: %d。%s",
                successCount, failCount, failCount > 0 ? "\n失败详情:\n" + resultMessage : "");

        return Response.success(finalResult);
    }

}