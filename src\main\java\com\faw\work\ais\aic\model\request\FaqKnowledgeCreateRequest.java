package com.faw.work.ais.aic.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * FAQ知识请求对象
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(description = "FAQ知识请求对象")
public class FaqKnowledgeCreateRequest {
    @Schema(description = "类目ID")
    private String categoryId;
    
    @NotBlank(message = "FAQ题目不能为空")
    @Schema(description = "FAQ题目")
    private String question;
    
    @NotBlank(message = "答案不能为空")
    @Schema(description = "答案")
    private String answer;


    @Schema(description = "生效类型 00-永久有效 01-临时有效")
    private String effectiveType;
    @Schema(description = "生效开始时间 yyyy-MM-dd HH:mm:ss")
    private String effectiveStartTime;
    @Schema(description = "生效结束时间 yyyy-MM-dd HH:mm:ss")
    private String effectiveEndTime;

    @Schema(description = "相似问题")
    private List<String> similarQuestionList;
    /**
     * 答案类型 00-纯文本 01-富文本
     */
    private String answerType;

}
