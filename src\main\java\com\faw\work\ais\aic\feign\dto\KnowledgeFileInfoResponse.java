package com.faw.work.ais.aic.feign.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(title = "知识附件info", description = "知识附件info")
public class KnowledgeFileInfoResponse {
    /**
     * 主键id
     */
    @JsonProperty("fileId")
    @Schema(description = "文件id")
    private Integer id;

    /**
     * 附件类型;字典
     */
    @Schema(description = "附件类型")
    private Integer fileType;

    /**
     * 附件路径
     */
    @Schema(description = "附件路径")
    private String filePath;

    /**
     * 附件名称
     */
    @Schema(description = "附件名称")
    private String fileName;

    @Schema(description = "附件大小")
    private Integer fileSize;

    @Schema(description = "附件大小（格式化）")
    private String formatSize;

    /**
     * 附件路径
     */
    @Schema(description = "附件全路径")
    private String fileFullPath;

    @Schema(description = "下载临时路径")
    private String downloadTempUrl;

    /**
     * 附件类型名称
     */
    @Schema(description = "附件类型名称")
    private String fileTypeName;


    /**
     * 类目名称
     */
    @Schema(description = "类目名称")
    private String label;
}
