package com.faw.work.ais.entity.dto.ai;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * ai任务算法表
 */
@Data
public class TaskRuleDTO {
    @Schema(description = "规则ID")
    private Integer id;
    @Schema(description = "规则名称")
    private String taskName;
    @Schema(description = "业务单元编码")
    private String bizType;
    @Schema(description = "规则描述")
    private String desc;
    @Schema(description = "系统编号")
    private String systemId;
    @Schema(description="当前页数")
    private Integer currentPage = 1;

    @Schema(description="分页条数")
    private Integer pageSize = -1;
}