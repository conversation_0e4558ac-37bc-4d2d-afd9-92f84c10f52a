package com.faw.work.ais.aic.service;

import com.faw.work.ais.aic.model.request.ProcessRequest;
import com.faw.work.ais.aic.model.response.MessageQueueCleanResponse;

/**
 * <AUTHOR>
 */
public interface LlmRecordService {
    /**
     * 处理对话
     *
     * @param request 处理请求
     */
    void processConversation(ProcessRequest request);

    /**
     * 重试失败的记录
     *
     * @param requestId 请求ID
     * @return 是否重试成功
     */
    boolean retryFailedRecords(String requestId);

    /**
     * 清理已完成的消息队列数据
     *
     * @return 清理结果
     */
    MessageQueueCleanResponse cleanCompletedMessages();

    /**
     * 处理分片数据
     *
     * @param requestId 请求ID
     * @return 处理结果
     */
    boolean processSlice(String requestId);

    /**
     * 更新状态（新事务）
     *
     * @param id     记录ID
     * @param status 状态
     * @param remark 备注
     */
    void updateStatusTran(Long id, String status, String remark);

    void cleanRequestData(String messageId);
}
