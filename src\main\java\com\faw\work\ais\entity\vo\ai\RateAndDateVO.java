package com.faw.work.ais.entity.vo.ai;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 日期对应的准确率
 */
@Data
public class RateAndDateVO {

    @Schema(description = "规则日期开始时间")
    private String billDateBegin;

    @Schema(description = "规则日期结束时间")
    private String billDateEnd;

    @Schema(description = "规则准确率")
    private String billRate;

    @Schema(description = "较昨日数据")
    private String compareYesterdayRate;

}
