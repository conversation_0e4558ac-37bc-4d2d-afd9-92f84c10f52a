package com.faw.work.ais.aic.common.mq;

import com.faw.work.ais.aic.common.enums.MessageStatus;
import com.faw.work.ais.aic.common.enums.MessageTypeEnum;
import com.faw.work.ais.aic.mapper.MessageQueueMapper;
import com.faw.work.ais.aic.model.domain.MessageQueue;
import com.faw.work.ais.aic.service.LlmRecordService;
import com.faw.work.ais.aic.service.MessageQueueService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RefreshScope
public class MessageConsumer {

    @Value("${mq.openCleanData:true}")
    private boolean openCleanData;

    @Autowired
    private LlmRecordService llmRecordService;
    @Autowired
    private MessageQueueMapper messageQueueMapper;

    @Autowired
    private MessageQueueService messageQueueService;

    @RabbitListener(queues = "${rabbitmq.queue.name:message-processing-queue}")
    public void processMessage(String content, Message message) throws InterruptedException {
        String messageId = message.getMessageProperties().getMessageId();
        log.info("收到消息，ID: {}, 内容: {}", messageId, content);

        // 轮询等待数据可用,避免事务还没有提交 导致数据查询不到
        MessageQueue messageQueue = null;
        int retryCount = 0;
        int maxRetries = 5;

        while (messageQueue == null && retryCount < maxRetries) {
            messageQueue = messageQueueMapper.selectByMessageId(messageId);
            if (messageQueue == null) {
                log.info("消息未入库，等待500ms后重试，重试次数: {}/{}", retryCount + 1, maxRetries);

                Thread.sleep(500);
                retryCount++;
            }
        }

        if (messageQueue == null) {
            log.error("消息在数据库中不存在，ID: {}", messageId);
            return;
        }

        if (messageQueue.getStatus() != MessageStatus.UNPROCESSED.getCode()) {
            log.error("消息已被处理或状态不合法，跳过处理。ID: {}", messageId);
            return;
        }
        try {
            // 1. 状态校验与更新为处理中（独立事务）
            messageQueueService.updateStatus(messageId, MessageStatus.PROCESSING, null);
            log.info("开始处理消息，ID: {}", messageId);
            if (MessageTypeEnum.DMS_EMOTION_PRODUCT.getCode().equals(messageQueue.getBizType())) {
                llmRecordService.processSlice(messageQueue.getMessageContent());
            }

            // 处理成功后
            messageQueueService.updateStatus(messageId, MessageStatus.PROCESSED, null);

            log.info("消息处理成功，开始清理消息，ID: {}", messageId);
            // 清理消息队列和分片数据
            if (openCleanData) {
                llmRecordService.cleanRequestData(messageId);
            }
        } catch (Exception e) {
            log.error("消息处理失败，ID: " + messageId, e);
            messageQueueService.updateStatus(messageId, MessageStatus.FAILED, "处理失败原因: " + e.getMessage());
            // 抛出特定异常使消息重新入队，会进入死信队列,这里不在抛出，因为本地消息表已经记录了信息
            // throw new AmqpRejectAndDontRequeueException("处理失败", e);
        }
    }
}