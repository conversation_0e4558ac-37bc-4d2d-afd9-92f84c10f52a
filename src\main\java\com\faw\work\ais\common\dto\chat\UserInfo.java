package com.faw.work.ais.common.dto.chat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 用户信息
 *
 * <AUTHOR>
 * @since 2025-06-23 15:29
 */
@Data
@Schema(description = "用户信息")
public class UserInfo {

    /**
     * 用户编码
     */
    @Schema(description = "用户编码")
    private String userCode;

    /**
     * 性别
     */
    @Schema(description = "性别")
    private String gender;

    /**
     * 车辆vin
     */
    @Schema(description = "车辆vin")
    private String vin;

    /**
     * 城市编码
     */
    @Schema(description = "城市编码")
    private String cityCode;

    /**
     * 经度
     */
    @Schema(description = "经度")
    private String lon;

    /**
     * 纬度
     */
    @Schema(description = "纬度")
    private String lat;

    /**
     * 手机号
     */
    @Schema(description = "手机号")
    private String phone;

}
