package com.faw.work.ais.entity.dto.ai;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * AI看板筛选入参
 */
@Data
public class AiKanBanDTO {

    @ApiModelProperty(value = "系统id")
    private String systemId;

    @ApiModelProperty(value = "AI审核开始日期")
    private String aiCheckTimeStart;

    @ApiModelProperty(value = "AI审核结束日期")
    private String aiCheckTimeEnd;

    @ApiModelProperty(value = "人工审核开始日期")
    private String humanCheckTimeStart;

    @ApiModelProperty(value = "人工审核结束日期")
    private String humanCheckTimeEnd;

    @ApiModelProperty(value = "单据准确率选中标识；0-未选中；1-选中；和规则准确率互斥，默认为0")
    private String billRightRateFlag;

    @ApiModelProperty(value = "规则准确率选中标识；0-未选中；1-选中；和规则准确率互斥，默认为0")
    private String ruleCheckRateFlag;

    @ApiModelProperty(value = "统计类型；1-日；2-周；3-月")
    private String totalType;

    @Schema(description = "单据id")
    private String batchId;

    @Schema(description = "请求的唯一id")
    private String traceId;

    @Schema(description = "规则id")
    private String taskType;

}
