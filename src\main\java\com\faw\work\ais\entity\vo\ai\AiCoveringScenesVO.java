package com.faw.work.ais.entity.vo.ai;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 数字员工（AI覆盖场景概览返回对象）
 * <AUTHOR>
 */
@Data
@Schema(description = "AI覆盖场景概览返回对象")
public class AiCoveringScenesVO {

    @Schema(description = "id")
    private Integer id;

    @Schema(description = "AI覆盖角色数")
    private Integer aiCoverRoleNum;

    @Schema(description = "AI覆盖业务单元数")
    private Integer aiCoverBizNum;

    @Schema(description = "AI覆盖规则数")
    private Integer aiCoverRuleNum;

    @Schema(description = "规则满足上线数量")
    private Integer onlineCount;

    @Schema(description = "AI规则上线覆盖率")
    private BigDecimal onlineCoverageRate;
}
