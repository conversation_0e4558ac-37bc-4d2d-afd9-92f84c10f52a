package com.faw.work.ais.aic.common.aop;


import com.faw.work.ais.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@Aspect
public class LockAspect {

    @Autowired
    private RedissonClient redissonClient;



    @Pointcut("@annotation(com.faw.work.ais.aic.common.aop.IdempotentLock)")
    public void lockAspect() {

    }

    @Around("lockAspect() && @annotation(aopLock)")
    public Object around(ProceedingJoinPoint joinPoint, IdempotentLock aopLock) throws Throwable {
        String lockName = aopLock.lockName();
        long duration = aopLock.duration();
        String prefix = aopLock.prefix();
        if (StringUtils.isNotBlank(lockName)) {
            lockName = aopLock.lockName();
        } else if (StringUtils.startsWith(aopLock.expression(), "#")) {
            String key = aopLock.expression();
            DefaultParameterNameDiscoverer parameterNameDiscoverer= new DefaultParameterNameDiscoverer();
            SpelExpressionParser parserSpel = new SpelExpressionParser();
            // 解析 SpEL 表达式
            Expression expression = parserSpel.parseExpression(key);

            // 创建一个标准的评估上下文
            EvaluationContext context = new StandardEvaluationContext();


            // 获取方法参数值
            Object[] args =  joinPoint.getArgs();

            // 获取方法名
            Method method=null;
            String methodName = joinPoint.getSignature().getName();
            Class<?> targetClass = joinPoint.getTarget().getClass();

            for (Method temp : targetClass.getDeclaredMethods()) {
                if (temp.getName().equals(methodName)) {
                   method = temp;
                    break;
                }
            }
            String[] paramNames = parameterNameDiscoverer.getParameterNames(method);

            // 将方法参数值设置到评估上下文中
            for (int i = 0; i < args.length; i++) {
                context.setVariable(paramNames[i], args[i]);
            }

            // 根据 SpEL 表达式计算出结果并转换为字符串
            lockName = Objects.toString(expression.getValue(context));
        } else {
            // 如果未定义lockName和SPEL,就将所有参数拼接
            lockName = Arrays.stream(joinPoint.getArgs())
                    .map(Object::toString)
                    .collect(Collectors.joining(","));
        }
        if(StringUtils.isNotBlank(prefix)){
            lockName = prefix + ":" + lockName;
        }
        log.info("IdempotentLock lockName: " + lockName);
        RLock lock = redissonClient.getLock(lockName);
        boolean isLock = false;
        try {
            isLock = lock.tryLock(500, duration*1000L,TimeUnit.MILLISECONDS);
        } catch (InterruptedException e) {
            throw new BizException("获取锁失败：" + e.getMessage());
        }
        Object obj = null;
        if (isLock) {
            try {
                // 业务代码
                obj = joinPoint.proceed();
            } catch (Throwable e) {
                throw e;
            }
            finally {
                // 释放锁
                lock.unlock();
            }
        } else {
            String description = aopLock.description();
            long time = aopLock.duration();
            throw new BizException(description+"，请"+time+"秒后重试");
        }

        return obj;
    }
}
