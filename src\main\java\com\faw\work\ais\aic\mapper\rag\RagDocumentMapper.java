package com.faw.work.ais.aic.mapper.rag;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.faw.work.ais.aic.model.domain.RagDocumentPO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 文档表 Mapper 接口
 *
 * <AUTHOR> Assistant
 */
@Mapper
public interface RagDocumentMapper extends BaseMapper<RagDocumentPO> {

    /**
     * 条件查询文档列表
     *
     * @param ragDocumentPO 查询条件
     * @return 文档列表
     */
    List<RagDocumentPO> getDocumentList(RagDocumentPO ragDocumentPO);

    /**
     * 按应用程序id获取列表
     *
     * @param agentId   应用ID
     * @param bizInfo 我们信息
     * @return {@link List }<{@link RagDocumentPO }>
     */
    List<RagDocumentPO> getDocumentListByAppId(Long agentId, String bizInfo);

    RagDocumentPO getDetailById(Long documentId);
}