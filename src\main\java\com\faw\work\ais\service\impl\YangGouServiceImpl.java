package com.faw.work.ais.service.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.faw.work.ais.aic.common.util.BaiLianUtils;
import com.faw.work.ais.aic.common.util.StrUtils;
import com.faw.work.ais.aic.config.BaiLianAppConfig;
import com.faw.work.ais.aic.model.request.FaqDogKnowledgeInsertRequest;
import com.faw.work.ais.aic.model.request.FaqSearchByRobotRequest;
import com.faw.work.ais.aic.model.request.SearchContentRequest;
import com.faw.work.ais.aic.model.response.FaqKnowledgeResponse;
import com.faw.work.ais.aic.model.response.SimilarContentSearchResponse;
import com.faw.work.ais.aic.service.FaqKnowledgeService;
import com.faw.work.ais.aic.service.RagDocumentService;
import com.faw.work.ais.common.CommonConstants;
import com.faw.work.ais.common.dto.CustomAttribute;
import com.faw.work.ais.common.dto.DogResponse;
import com.faw.work.ais.common.dto.chat.AnalysisRequest;
import com.faw.work.ais.common.dto.chat.AnalysisResponse;
import com.faw.work.ais.common.dto.chat.MessageEntity;
import com.faw.work.ais.config.YangGouConfig;
import com.faw.work.ais.entity.dto.OpsPromptGenerationDto;
import com.faw.work.ais.feign.AliClient;
import com.faw.work.ais.mapper.ais.PromptTemplateMapper;
import com.faw.work.ais.model.KnowledgeUpdateItem;
import com.faw.work.ais.model.PromptTemplate;
import com.faw.work.ais.model.PromptUpdateItem;
import com.faw.work.ais.service.YangGouService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class YangGouServiceImpl  extends ServiceImpl<PromptTemplateMapper, PromptTemplate> implements YangGouService {

    @Autowired
    private BaiLianAppConfig baiLianAppConfig;

    @Autowired
    private YangGouConfig yangGouConfig;

    @Autowired
    private PromptTemplateMapper promptTemplateMapper;

    @Autowired
    private PromptService promptService;

    @Autowired
    private AliClient aliClient;

    @Autowired
    private FaqKnowledgeService faqKnowledgeService;

    @Autowired
    private RagDocumentService ragDocumentService;


    @Override
    @Async
    public void updateTemplate(List<OpsPromptGenerationDto> opsPromptGenerationDtoList) {
        for (OpsPromptGenerationDto opsPromptGenerationDto : opsPromptGenerationDtoList) {
            // 优质话术提取
            String result = BaiLianUtils.callForObject(
                    yangGouConfig.getWorkspaceId(),
                    yangGouConfig.getApiKey(),
                    yangGouConfig.getYanggouHighQualitySpeech(),
                    "通话文本："+
                            opsPromptGenerationDto.getCallContent()+
                            "指标结果：" +
                            "已接通、"+ (opsPromptGenerationDto.getSchedule().equals("1") ? "已排程":"未排程")+ (opsPromptGenerationDto.getValid().equals("1") ? "有效":"无效")+
                            "策略内容："+
                            JSON.toJSONString(opsPromptGenerationDto.getRecommendList()),
                    String.class);
            log.info("优质话术（提示词）提取智能体:{}", result);
            opsPromptGenerationDto.setHighQualitySpeech(result);
        }

        // 查询最新的prompt
        PromptTemplate one = getLatestPromptTemplateConfig();
        if(one == null){
            one = new PromptTemplate();
        }
        // 优质prompt
        String prompt = "";
        for (int i = 0; i < opsPromptGenerationDtoList.size(); i++) {
            prompt  = prompt +  "优质话术"+ (i+1) +"："+ opsPromptGenerationDtoList.get(i).getHighQualitySpeech();
        }
        prompt = prompt + "历史优质话术：" + one.getPrompt();

        String result = BaiLianUtils.callForObject(
                yangGouConfig.getWorkspaceId(),
                yangGouConfig.getApiKey(),
                yangGouConfig.getYanggouHighQualityPrompt(),
                prompt,
                String.class);
        result = StrUtils.cleanJsonString(result);
        log.info("prompt优化模型返回结果：{}",result);
        String newPrompt = generateNewPrompt(one.getPrompt(), result);
        PromptTemplate promptTemplate = new PromptTemplate();
        promptTemplate.setPrompt(newPrompt);
        promptTemplate.setUpdateContent(result);
        promptTemplate.setCreateTime(Timestamp.valueOf(LocalDateTime.now()));
        promptTemplate.setVersion(one.getVersion()==null?0: one.getVersion()+1);
        promptTemplateMapper.insert(promptTemplate);
        // 调用知识库更新
        FaqDogKnowledgeInsertRequest faqKnowledgeRequest = new FaqDogKnowledgeInsertRequest();
        List<FaqDogKnowledgeInsertRequest.KnowledgeItem> knowledgeItemList = new ArrayList<>();
        for (OpsPromptGenerationDto opsPromptGenerationDto : opsPromptGenerationDtoList) {
            try {
                String highQualityKnowledge = BaiLianUtils.callForObject(
                        yangGouConfig.getWorkspaceId(),
                        yangGouConfig.getApiKey(),
                        yangGouConfig.getHighQualityKnowledge(),
                        "通话文本："+
                                opsPromptGenerationDto.getCallContent()+
                                "指标结果：" +
                                "已接通、"+ (opsPromptGenerationDto.getSchedule().equals("1") ? "已排程":"未排程")+ (opsPromptGenerationDto.getValid().equals("1") ? "、有效。":"、无效。")+
                                "策略内容："+
                                JSON.toJSONString(opsPromptGenerationDto.getRecommendList()),
                        String.class);
                log.info("优质知识提取智能体:{}", highQualityKnowledge);
                List<KnowledgeUpdateItem> knowledgeUpdateItems = JSON.parseArray(StrUtils.cleanJsonString(highQualityKnowledge), KnowledgeUpdateItem.class);
                for (KnowledgeUpdateItem knowledgeUpdateItem : knowledgeUpdateItems) {
                    FaqDogKnowledgeInsertRequest.KnowledgeItem knowledgeItem = new FaqDogKnowledgeInsertRequest.KnowledgeItem();
                    knowledgeItem.setQuestion(knowledgeUpdateItem.getCustomerProfile()+ knowledgeUpdateItem.getScriptType()+knowledgeUpdateItem.getCustomerTraits());
                    knowledgeItem.setAnswer(knowledgeUpdateItem.getScriptContent());
                    knowledgeItem.setKnowledgeVersion(one.getVersion()==null?0: one.getVersion());
                    knowledgeItemList.add(knowledgeItem);
                }
            } catch (Exception e) {
                log.warn("优质知识提取智能体:{}", e);
            }
        }
        faqKnowledgeRequest.setKnowledgeList(knowledgeItemList);
        faqKnowledgeRequest.setRobotId(yangGouConfig.getRobotId());
        // 调用王鑫知识库
        log.info("知识库更新入参{}" , JSON.toJSONString(faqKnowledgeRequest));
        faqKnowledgeService.dogKnowledgeInsert(faqKnowledgeRequest);
    }

    private String generateNewPrompt(String oldConfig, String newConfig) {
        if(StringUtils.hasLength(oldConfig)){
            List<PromptUpdateItem> oldPromptConfigList = JSON.parseArray(oldConfig, PromptUpdateItem.class);
            List<PromptUpdateItem> promptUpdateItems = JSON.parseArray(newConfig, PromptUpdateItem.class);
            for (PromptUpdateItem promptUpdateItem : promptUpdateItems) {
                boolean hasKey = false;
                for (PromptUpdateItem item : oldPromptConfigList) {
                    if(Objects.equals(item.getId(), promptUpdateItem.getId())){
                        item.setValue(promptUpdateItem.getValue());
                        hasKey = true;
                        break;
                    }
                }
                if(!hasKey){
                    oldPromptConfigList.add(promptUpdateItem);
                }
            }
            return JSON.toJSONString(oldPromptConfigList);
        }else {
            return newConfig;
        }
    }


    @Override
    public PromptTemplate getLatestPromptTemplate() {
        PromptTemplate latestPromptTemplateConfig = getLatestPromptTemplateConfig();
        String promptTextTemplate = promptService.getPromptText();
        String prompt = latestPromptTemplateConfig.getPrompt();
        List<PromptUpdateItem> promptUpdateItems = JSON.parseArray(prompt, PromptUpdateItem.class);
        for (PromptUpdateItem promptUpdateItem : promptUpdateItems) {
            promptTextTemplate = promptTextTemplate.replace("{"+ promptUpdateItem.getId()+"}", promptUpdateItem.getValue());
        }
        latestPromptTemplateConfig.setPrompt(promptTextTemplate);
        return latestPromptTemplateConfig;
    }
    private PromptTemplate getLatestPromptTemplateConfig() {
        LambdaQueryWrapper<PromptTemplate> query = new LambdaQueryWrapper<>();
        query.orderByDesc(PromptTemplate::getCreateTime);
        query.last("LIMIT 1");
        return this.getOne(query);
    }

    @Override
    public DogResponse generate(String callText) {
        long start = System.currentTimeMillis();
        /**
         * 生成客户画像&&总结内容
         */
        long step1Start = System.currentTimeMillis();
        CustomAttribute customAttribute = BaiLianUtils.callForObject(
                yangGouConfig.getWorkspaceId(),
                yangGouConfig.getApiKey(),
                yangGouConfig.getKehuhuaxiangAppId(),
                callText,
                CustomAttribute.class
        );
        List<String> scriptList = new ArrayList<>(Arrays.asList(
                "通话小结", "破冰话术", "破冰拒绝引导话术", "车型整体介绍话术",
                "客户正在比较其他车型", "车辆置换补贴话术", "预算顾虑话术",
                "售后服务顾虑", "加微信话术", "排程到店话术"
        ));
        if (customAttribute != null){
            log.info("客户画像：" + customAttribute.getCustomerProfile());
            log.info("总结内容：" + customAttribute.getCustomerTraits());
        }
        long step1End = System.currentTimeMillis();
        log.info("步骤1（生成客户画像&&总结内容）耗时：{} ms", (step1End - step1Start));

        /**
         * 每个话术类型召回一条知识循环
         */
        long step2Start = System.currentTimeMillis();
        String knowledge1 = "";
        if (!"".equals(customAttribute.getCustomerProfile()) && !"".equals(customAttribute.getCustomerTraits())){
            for (String script : scriptList) {
                String input = customAttribute.getCustomerProfile() + "-"
                        + customAttribute.getCustomerTraits()
                        + "-" + script;
                String env = yangGouConfig.getEnv();
                String robotId = yangGouConfig.getRobotId();
                Integer topK = yangGouConfig.getTopK();
                float similarityThreshold = yangGouConfig.getSimilarityThreshold();
                FaqSearchByRobotRequest faqSearchByRobotRequest = FaqSearchByRobotRequest.of(robotId, input, topK, similarityThreshold, env, null, null);
                List<FaqKnowledgeResponse> faqKnowledgeResponses = faqKnowledgeService.searchByRobotId(faqSearchByRobotRequest);
                if (!faqKnowledgeResponses.isEmpty()){
                    FaqKnowledgeResponse res = faqKnowledgeResponses.get(0);
                    String answer = res.getAnswer();
                    knowledge1 += script + "示例：" + answer + "\n";
                }
//            String scriptResult = answer;
//            knowledge1 += script + "示例：" + scriptResult + "\n";
            }
        }
        log.info("结构化知识数据：" + knowledge1);
        long step2End = System.currentTimeMillis();
        log.info("步骤2（结构化知识召回）耗时：{} ms", (step2End - step2Start));

        /**
         * 通话文本召回excel导入知识
         */
        long step3Start = System.currentTimeMillis();
        String excelKnowledge = "";
        SearchContentRequest req = new SearchContentRequest();
        req.setRagKnowledgeId(3L);
        req.setQuery(callText);
        req.setLabel("养狗");
        req.setChatHistory("");

        List<SimilarContentSearchResponse> results = ragDocumentService.searchSimilarContentNew(req);

        if (!results.isEmpty()) {
            String joinedContent = results.stream()
                    .map(SimilarContentSearchResponse::getDocumentContent)
                    .filter(Objects::nonNull)
                    .collect(Collectors.joining(","));
            excelKnowledge = joinedContent;
        }

        log.info("非结构化知识数据：" + excelKnowledge);
        long step3End = System.currentTimeMillis();
        log.info("步骤3（非结构化知识召回）耗时：{} ms", (step3End - step3Start));
        /**
         * 获取最新版本prompt模板
         */
        long step4Start = System.currentTimeMillis();
        PromptTemplate latestPromptTemplate = this.getLatestPromptTemplate();
        String systemPrompt = latestPromptTemplate.getPrompt();
        Integer version = latestPromptTemplate.getVersion();
        /**
         * prompt拼接
         */

        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String formattedDateTime = now.format(formatter);

        StringBuilder userPrompt = new StringBuilder("当前时间：" + formattedDateTime + "\n" + "通话文本：" + callText + "\n");
        if (knowledge1 != null && !knowledge1.isEmpty()) {
            userPrompt.append("召回知识1：\n").append(knowledge1);
        }
        if (excelKnowledge != null && !excelKnowledge.isEmpty()) {
            userPrompt.append("召回知识2：\n").append(excelKnowledge);
        }

        String userPromptStr = userPrompt.toString();

        log.info("systemPrompt:" + systemPrompt);
        log.info("策略生成prompt:" + userPrompt);
        long step4End = System.currentTimeMillis();
        log.info("步骤4（获取最新prompt模板）耗时：{} ms", (step4End - step4Start));
        /**
         * 生成策略
         */
        long step5Start = System.currentTimeMillis();
        // System message
        MessageEntity systemMessage = new MessageEntity();
        systemMessage.setRole("system");
        systemMessage.setContent(List.of(
                Map.of("type", "text", "text", systemPrompt)
        ));

        // User message
        MessageEntity userMessage = new MessageEntity();
        userMessage.setRole("user");

        // 创建包含所有图片和文本的内容列表
        List<Map<String, Object>> contentList = new ArrayList<>();

        contentList.add(Map.of("type", "text", "text", userPromptStr));
        userMessage.setContent(contentList);

        AnalysisRequest request = new AnalysisRequest();
        request.setModel("qwen-max");
        request.setTemperature(0.01f);
        request.setMessages(List.of(systemMessage, userMessage));

        String strategy = "";
        AnalysisResponse response = aliClient.analyze(request, yangGouConfig.getApiKey());
        if (Objects.nonNull(response) && !CollectionUtils.isEmpty(response.getChoices())) {
            strategy = response.getChoices().get(CommonConstants.FIRST_INDEX).getMessage().getContent();
        }
        strategy = formatJson(strategy);
        // 将strategy转换为JSONArray
        JSONArray jsonArray = JSON.parseArray(strategy);
        DogResponse dogResponse = new DogResponse();
        dogResponse.setStrategy(jsonArray);
        dogResponse.setVersion(version);
        long step5End = System.currentTimeMillis();
        log.info("步骤5（生成策略）耗时：{} ms", (step5End - step5Start));
        long end = System.currentTimeMillis();
        log.info("generate方法总耗时：{} ms", (end - start));
        return dogResponse;
    }

    private String formatJson(String res) {
        return res.replaceAll("json", "")  // 删除开头标记
                .replaceAll("```", "")         // 删除结尾标记
                .trim();                       // 清理首尾空格
    }
}