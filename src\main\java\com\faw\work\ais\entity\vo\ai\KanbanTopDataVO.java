package com.faw.work.ais.entity.vo.ai;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 看板列表
 */
@Data
public class KanbanTopDataVO {

    @ApiModelProperty("单据审核准确率")
    private String billCheckRightRate;

    @Schema(description = "AI和人工审核一致的数量；即 ai通过且人工通过+ai驳回且人工驳回")
    private String aiPassHumanPassSumAiBackHumanBac;

    @ApiModelProperty("AI审核单据数量-需求中无描述")
    private String aiCheckBillNum;

    @ApiModelProperty("人工复审单据数量-需求中无描述")
    private String hunmanCheckBillNum;

    @ApiModelProperty("AI审核通过(数量-单据维度)")
    private String aiPassNum;

    @ApiModelProperty("准确率-(AI审核通过)")
    private String aiPassRate;

    @ApiModelProperty("人工复审通过-AI审核通过-单据维度-需求没描述")
    private String aiPassHunmanCheckPassNum;

    @ApiModelProperty("人工复审驳回-AI审核通过-单据维度-需求没描述")
    private String aiPassHunmanCheckRackBackNum;

    @ApiModelProperty("AI审核驳回(数量-单据维度)")
    private String aiBackNum;

    @ApiModelProperty("准确率-AI审核驳回(单据维度)")
    private String aiBackRate;

    @ApiModelProperty("AI审核驳回-人工复审通过单据维度")
    private String aiBackHumanPassNum;

    @ApiModelProperty("AI审核驳回-人工复审驳回单据维度")
    private String aiBackHumanBackNum;

    @ApiModelProperty("规则维度的数据")
    private KanBanTopDataRuleVO ruleData;

    @ApiModelProperty("看板的数据列表")
    private List<KanBanBillListVO> kanBanBillDatas;
}
