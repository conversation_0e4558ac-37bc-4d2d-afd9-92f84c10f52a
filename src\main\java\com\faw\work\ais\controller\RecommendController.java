package com.faw.work.ais.controller;

import com.faw.work.ais.common.Response;
import com.faw.work.ais.common.dto.chat.RecommendRequest;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.faw.work.ais.service.RecommendService;

import java.util.List;

@RestController
@RequestMapping("/recommend")
@Tag(name = "猜你喜欢", description = "猜你喜欢相关接口")
@Slf4j
@RequiredArgsConstructor
public class RecommendController {

    private final RecommendService recommendService;

    @PostMapping("/question")
    @Operation(summary = "10条推荐问题", description = "10条推荐问题")
    public Response<List<String>> recommend(@RequestBody @Valid RecommendRequest request) {
        log.info("接收到猜你喜欢请求: appId:{},用户id:{}", request.getRobotId(), request.getUserId());
        List<String> recommendations = recommendService.getRecommendations(request);
        return Response.success(recommendations);
    }

}
